<?php

use <PERSON><PERSON>al\taxonomy\Entity\Term;
use Drupal\Core\Entity\EntityTypeManagerInterface;

class ReglementationImporter {

  protected $entityTypeManager;

  public function __construct(EntityTypeManagerInterface $entityTypeManager) {
    $this->entityTypeManager = $entityTypeManager;
  }

  /**
   * Finds a taxonomy term TID by name and vocabulary.
   *
   * @param string $term_name
   *   The name of the term to search for.
   * @param string $vocabulary
   *   The vocabulary machine name.
   * @param string|null $langcode
   *   (optional) Language code to search in. Defaults to 'fr'.
   * @param bool $create_if_missing
   *   (optional) If TRUE, create the term if not found.
   *
   * @return int|null
   *   The TID if found (or created), or NULL.
   */
  public function getTidByTermName(string $term_name, string $vocabulary, ?string $langcode = 'fr', bool $create_if_missing = FALSE): ?int {
    $storage = $this->entityTypeManager->getStorage('taxonomy_term');

    // 1. Try to find the term in the given language.
    $query = $storage->getQuery()
      ->accessCheck(FALSE)
      ->condition('vid', $vocabulary)
      ->condition('name', $term_name)
      ->condition('langcode', $langcode)
      ->range(0, 1);

    $tids = $query->execute();
    if (!empty($tids)) {
      return reset($tids);
    }

    // 2. Try to find the term in any language (multilingual support).
    $query = $storage->getQuery()
      ->accessCheck(FALSE)
      ->condition('vid', $vocabulary)
      ->condition('name', $term_name)
      ->range(0, 1);

    $tids = $query->execute();
    if (!empty($tids)) {
      return reset($tids);
    }

    // 3. Optionally, create the term if not found.
    if ($create_if_missing) {
      $term = Term::create([
        'vid' => $vocabulary,
        'name' => $term_name,
        'langcode' => $langcode ?? 'fr',
      ]);
      $term->save();
      return $term->id();
    }

    return NULL;
  }

  /**
   * Finds a taxonomy term by name and vocabulary.
   *
   * @param string $term_name
   *   The name of the term to search for.
   * @param string $vocabulary
   *   The vocabulary machine name.
   *
   * @return \Drupal\taxonomy\Entity\Term|null
   *   The taxonomy term entity if found, or NULL otherwise.
   */
  private function findTaxonomyTermByName(string $term_name, string $vocabulary) {
    // Afficher les informations de recherche
    \Drupal::logger('import_reglementation')->notice('Recherche du terme: "@name" dans le vocabulaire "@vocabulary"', [
      '@name' => $term_name,
      '@vocabulary' => $vocabulary
    ]);

    $term_storage = $this->entityTypeManager->getStorage('taxonomy_term');

    // Rechercher le terme exact
    $query = $term_storage->getQuery()
      ->accessCheck(FALSE)
      ->condition('name', $term_name)
      ->condition('vid', $vocabulary)
      ->range(0, 1);

    $tids = $query->execute();

    if (!empty($tids)) {
      $tid = reset($tids);
      $term = $term_storage->load($tid);
      \Drupal::logger('import_reglementation')->notice('Terme de taxonomie trouvé: "@name" (@vocabulary) - ID: @tid', [
        '@name' => $term_name,
        '@vocabulary' => $vocabulary,
        '@tid' => $tid
      ]);
      return $term;
    }

    // Essayer une recherche insensible à la casse
    $database = \Drupal::database();
    $query = $database->select('taxonomy_term_field_data', 't')
      ->fields('t', ['tid'])
      ->condition('t.vid', $vocabulary)
      ->condition('t.name', $term_name, 'LIKE')
      ->range(0, 1);

    $result = $query->execute()->fetchField();

    if ($result) {
      $tid = $result;
      $term = $term_storage->load($tid);
      \Drupal::logger('import_reglementation')->notice('Terme de taxonomie trouvé (recherche insensible à la casse): "@name" (@vocabulary) - ID: @tid', [
        '@name' => $term_name,
        '@vocabulary' => $vocabulary,
        '@tid' => $tid
      ]);
      return $term;
    }

    \Drupal::logger('import_reglementation')->warning('Terme de taxonomie non trouvé: "@name" (@vocabulary)', [
      '@name' => $term_name,
      '@vocabulary' => $vocabulary
    ]);
    return null;
  }

  /**
   * Importe les données depuis un fichier CSV pour le TID Transport routier = 3.
   * Ajoute uniquement les intitulés dans la langue spécifiée.
   *
   * @param string $filepath
   *   Le chemin absolu vers le fichier CSV.
   * @param string $langcode
   *   Le code de langue attendu (par exemple, 'fr').
   */
  public function importFromCsv(string $filepath, string $langcode = 'fr') {
    if (!file_exists($filepath) || !is_readable($filepath)) {
      \Drupal::logger('import_reglementation')->error('Fichier CSV non trouvé ou non lisible: @filepath', [
        '@filepath' => $filepath,
      ]);
      return;
    }

    if (($handle = fopen($filepath, 'r')) !== FALSE) {
      $header = fgetcsv($handle, 1000, ','); // Lire l'en-tête
      while (($data = fgetcsv($handle, 1000, ',')) !== FALSE) {
        // Supposons que le TID est dans la première colonne (index 0) et la langue dans la deuxième (index 1)
        if (isset($data[0]) && $data[0] == 3 && isset($data[1]) && $data[1] == $langcode) {
          // Ici, tu peux traiter la ligne comme tu veux
          // Exemple : log des données importées
          \Drupal::logger('import_reglementation')->notice('Importation ligne CSV: @data', [
            '@data' => implode(', ', $data),
          ]);

          // TODO: Ajouter ici le traitement spécifique (création d'entités, etc.)
        }
      }
      fclose($handle);
    }
    else {
      \Drupal::logger('import_reglementation')->error('Impossible d\'ouvrir le fichier CSV: @filepath', [
        '@filepath' => $filepath,
      ]);
    }
  }
} 